import os
import time
import argparse
import subprocess
from typing import List

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy
from sensor_msgs.msg import Image, JointState
from std_msgs.msg import Float64MultiArray
from cv_bridge import CvBridge
import numpy as np

JOINT_ORDER = [
    'Left_Arm_Joint1', 'Left_Arm_Joint2', 'Left_Arm_Joint3', 'Left_Arm_Joint4',
    'Left_Arm_Joint5', 'Left_Arm_Joint6', 'Left_Arm_Joint7',
    'Right_Arm_Joint1', 'Right_Arm_Joint2', 'Right_Arm_Joint3', 'Right_Arm_Joint4',
    'Right_Arm_Joint5', 'Right_Arm_Joint6', 'Right_Arm_Joint7',
]

class YmrobotToLerobot(Node):
    def __init__(self): 
        super().__init__('ymrobot_to_lerobot')

        self._init_episode_buffer()

        # === 辅助变量 ===
        self.bridge = CvBridge()
        
        # === QoS: 相机 BEST_EFFORT，其它 RELIABLE ===
        sensor_qos = QoSProfile(depth=10)
        sensor_qos.reliability = ReliabilityPolicy.BEST_EFFORT
        sensor_qos.history = HistoryPolicy.KEEP_LAST

        reliable_qos = QoSProfile(depth=20)
        reliable_qos.reliability = ReliabilityPolicy.RELIABLE
        reliable_qos.history = HistoryPolicy.KEEP_LAST

        # === 订阅者 ===
        # 图像
        self.create_subscription(Image, '/camera/color/image_raw', self.on_head_img, sensor_qos)
        self.create_subscription(Image, '/camera1/camera1/color/image_raw', self.on_left_img, sensor_qos)
        self.create_subscription(Image, '/camera2/camera2/color/image_raw', self.on_right_img, sensor_qos)
        # 状态
        self.create_subscription(JointState, '/joint_states', self.on_joint, reliable_qos)
        self.create_subscription(Float64MultiArray, '/gripper_states', self.on_gripper_state, reliable_qos)
        # 动作
        self.create_subscription(Float64MultiArray, '/left_arm_position_controller/commands', self.on_left_action, reliable_qos)
        self.create_subscription(Float64MultiArray, '/right_arm_position_controller/commands', self.on_right_action, reliable_qos)
        self.create_subscription(Float64MultiArray, '/gripper_commands', self.on_gripper_action, reliable_qos)

        self.get_logger().info('MinimalEpisodeNode started.')

    def on_left_img(self, msg: Image):
        try:
            self.left_img = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
        except Exception as e:
            self.get_logger().warn(f'left img convert failed: {e}')

    def on_right_img(self, msg: Image):
        try:
            self.right_img = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
        except Exception as e:
            self.get_logger().warn(f'right img convert failed: {e}')

    def on_joint(self, msg: JointState):
        self.joint_state = msg

    def on_gripper_state(self, msg: Float64MultiArray):
        self.gripper_state = np.asarray(msg.data, dtype=np.float32)  # (2,)

    def on_left_action(self, msg: Float64MultiArray):
        self.left_action = np.asarray(msg.data, dtype=np.float32)    # (7,)

    def on_right_action(self, msg: Float64MultiArray):
        self.right_action = np.asarray(msg.data, dtype=np.float32)   # (7,)

    def on_gripper_action(self, msg: Float64MultiArray):
        self.gripper_action = np.asarray(msg.data, dtype=np.float32) # (2,)

    def on_head_img(self, msg: Image):
        try:
            self.head_img = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
        except Exception as e:
            self.get_logger().warn(f'head img convert failed: {e}')
            return

        # 所有必需字段都不为 None 才追加
        if not self._ready():
            return

        # 组装 observation.state
        qpos = self._extract_qpos(self.joint_state, JOINT_ORDER)
        if qpos is None:
            # joint_state 不完整
            return

        # 长度基本校验
        if (self.left_action is None or self.left_action.size != 7 or
            self.right_action is None or self.right_action.size != 7 or
            self.gripper_state is None or self.gripper_state.size != 2 or
            self.gripper_action is None or self.gripper_action.size != 2):
            # 如需强制追加，去掉这些判断
            return

        state_vec = np.concatenate([
            qpos.astype(np.float32),
            self.gripper_state.astype(np.float32)
        ]).tolist()

        # action: [7*left, 7*right, 2*gripper] -> 16
        action_vec = np.concatenate([
            self.left_action.astype(np.float32),
            self.right_action.astype(np.float32),
            self.gripper_action.astype(np.float32)
        ]).tolist()

        # 追加
        self.episode["observation.state"].append(state_vec)
        self.episode["action"].append(action_vec)
        self.episode["observation.images.head_camera"].append(self.head_img.copy())
        self.episode["observation.images.left_wrist_camera"].append(self.left_img.copy())
        self.episode["observation.images.right_wrist_camera"].append(self.right_img.copy())

        self.sample_count += 1
        if self.sample_count % 50 == 0:
            h, w, _ = self.head_img.shape
            self.get_logger().info(f'[{self.sample_count}] appended | head={w}x{h} | episode_len={self.sample_count}')

    # ------- 小工具 -------
    def _ready(self) -> bool:
        return (self.head_img is not None and
                self.left_img is not None and
                self.right_img is not None and
                self.joint_state is not None and
                self.gripper_state is not None and
                self.left_action is not None and
                self.right_action is not None and
                self.gripper_action is not None)

    @staticmethod
    def _extract_qpos(joint_msg: JointState, order):
        if joint_msg is None:
            return None
        name_to_idx = {n: i for i, n in enumerate(joint_msg.name)}
        qpos = np.full((len(order),), np.nan, dtype=np.float32)
        filled = True
        for i, name in enumerate(order):
            idx = name_to_idx.get(name, None)
            if idx is None or idx >= len(joint_msg.position):
                filled = False
                break
            qpos[i] = float(joint_msg.position[idx])
        return qpos if filled else None
        
        
    def _init_episode_buffer(self):
        self.episode = {
            "observation.state": [],
            "action": [],
            "observation.images.head_camera": [],
            "observation.images.left_wrist_camera": [],
            "observation.images.right_wrist_camera": []
        }

        self.head_img = None
        self.left_img = None
        self.right_img = None
        self.joint_state = None
        self.gripper_state = None
        self.left_action = None
        self.right_action = None
        self.gripper_action = None

        self.sample_count = 0

    def save_data(self):
        pass

def find_bag_directories(root: str) -> List[str]:
    """返回 root 下所有 rosbag2 目录（包含 metadata.yaml）。若 root 本身是 bag，也会被返回。"""
    def is_bag_dir(p: str) -> bool:
        return os.path.isdir(p) and os.path.exists(os.path.join(p, 'metadata.yaml'))
    bags = []
    if is_bag_dir(root):
        bags.append(root)
    else:
        for name in sorted(os.listdir(root)):
            p = os.path.join(root, name)
            if is_bag_dir(p):
                bags.append(p)
    return bags

def play_one_bag(bag_dir: str, extra_args: List[str] = None) -> int:
    """阻塞直至播放完成，返回进程退出码。"""
    cmd = ['ros2', 'bag', 'play', bag_dir]
    if extra_args:
        cmd.extend(extra_args)
    print(f'[player] Exec: {" ".join(cmd)}')
    # 提前给订阅者一点时间（也可用 --start-paused 更严谨）
    time.sleep(0.5)
    return subprocess.run(cmd).returncode


def main():
    parser = argparse.ArgumentParser(description='Sequentially play ros2 bags and collect episodes.')
    parser.add_argument('root', type=str, help='包含多个 rosbag2 目录的路径；或单个 rosbag2 目录')
    parser.add_argument('--play-args', type=str, nargs=argparse.REMAINDER,
                        help='传给 ros2 bag play 的其它参数（例如 --rate 0.5 --clock）')
    args = parser.parse_args()

    bag_dirs = find_bag_directories(args.root)
    if not bag_dirs:
        print(f'No rosbag2 directories found under: {args.root}')
        return

    rclpy.init()
    node = YmrobotToLerobot()

    try:
        # 用独立线程执行器让订阅节点一直 spin
        executor = rclpy.executors.MultiThreadedExecutor()
        executor.add_node(node)

        import threading
        spin_thread = threading.Thread(target=executor.spin, daemon=True)
        spin_thread.start()

        for bag in bag_dirs:
            print(f'\n=== Playing: {bag} ===')
            rc = play_one_bag(bag, args.play_args)
            if rc != 0:
                print(f'[player] bag exited with code {rc} (skipping save)')
                node._init_episode_buffer()
                continue

            # 播放结束，稍等尾帧
            time.sleep(0.2)
            # 存一份本条 episode
            node.save_data()
            # 最后一行：清空 episode，准备下一条
            node._init_episode_buffer()

        # 播完了
        executor.shutdown()
        node.destroy_node()

    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()